/* Vegas Ace Slots - Premium Casino UI */

:root {
    --primary-gold: #FFD700;
    --secondary-purple: #8A2BE2;
    --accent-red: #DC143C;
    --neon-blue: #00FFFF;
    --neon-pink: #FF1493;
    --dark-bg: #0a0a0f;
    --card-bg: rgba(26, 26, 46, 0.85);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --shadow-glow: 0 0 30px rgba(255, 215, 0, 0.4);
    --text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
    --neon-glow: 0 0 20px currentColor;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background:
        radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(220, 20, 60, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #0f3460 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 2px,
            rgba(255, 215, 0, 0.03) 2px,
            rgba(255, 215, 0, 0.03) 4px
        );
    pointer-events: none;
    z-index: -1;
}

#game-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    backdrop-filter: blur(2px);
}

/* Header Styles - Premium Casino Design */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background:
        linear-gradient(135deg, var(--card-bg) 0%, rgba(138, 43, 226, 0.2) 100%),
        var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 3px solid transparent;
    border-image: linear-gradient(90deg, var(--primary-gold), var(--neon-pink), var(--primary-gold)) 1;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.game-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 215, 0, 0.1) 50%,
        transparent 100%);
    animation: shimmer 3s ease-in-out infinite;
}

.game-title {
    font-size: 2.2rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--primary-gold), var(--neon-pink), var(--primary-gold));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--text-shadow);
    animation: gradientShift 4s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

.game-stats {
    display: flex;
    gap: 25px;
    position: relative;
    z-index: 1;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 90px;
    padding: 8px 12px;
    background: var(--glass-bg);
    border-radius: 12px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
    border-color: var(--primary-gold);
}

.stat-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-gold);
    text-shadow: var(--neon-glow);
    transition: all 0.3s ease;
}

/* Game Canvas - Premium 3D Effect */
#phaser-game {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background:
        radial-gradient(ellipse at center, rgba(255, 215, 0, 0.15) 0%, transparent 70%),
        radial-gradient(circle at 30% 30%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(220, 20, 60, 0.1) 0%, transparent 50%);
    position: relative;
    overflow: hidden;
}

#phaser-game::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(255, 215, 0, 0.05) 49%, rgba(255, 215, 0, 0.05) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(138, 43, 226, 0.05) 49%, rgba(138, 43, 226, 0.05) 51%, transparent 52%);
    pointer-events: none;
}

/* Game Controls - Luxurious Design */
.game-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background:
        linear-gradient(135deg, var(--card-bg) 0%, rgba(220, 20, 60, 0.2) 100%),
        var(--glass-bg);
    backdrop-filter: blur(20px);
    border-top: 3px solid transparent;
    border-image: linear-gradient(90deg, var(--neon-pink), var(--primary-gold), var(--neon-pink)) 1;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.bet-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: 2px solid var(--primary-gold);
    background:
        linear-gradient(145deg, rgba(42, 42, 62, 0.9), rgba(26, 26, 46, 0.9)),
        var(--glass-bg);
    color: var(--primary-gold);
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.2rem;
    cursor: pointer;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    transition: left 0.5s ease;
}

.control-btn:hover {
    background:
        linear-gradient(145deg, rgba(58, 58, 78, 0.9), rgba(42, 42, 62, 0.9)),
        var(--glass-bg);
    box-shadow:
        var(--shadow-glow),
        inset 0 0 20px rgba(255, 215, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--neon-pink);
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn:active {
    transform: translateY(0) scale(0.95);
}

.bet-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 15px;
    padding: 12px 20px;
    background: var(--glass-bg);
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    backdrop-filter: blur(10px);
    min-width: 120px;
}

.bet-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 4px;
}

.bet-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-gold);
    text-shadow: var(--neon-glow);
}

/* Premium Spin Button */
.spin-button {
    width: 140px;
    height: 70px;
    border: 3px solid transparent;
    background:
        linear-gradient(145deg, var(--accent-red), #ff6b35, var(--accent-red)),
        linear-gradient(145deg, rgba(220, 20, 60, 0.9), rgba(255, 107, 53, 0.9));
    background-clip: padding-box, border-box;
    color: #fff;
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 900;
    cursor: pointer;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow:
        0 8px 25px rgba(220, 20, 60, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.spin-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.spin-button:hover {
    background:
        linear-gradient(145deg, #ff1744, #ff6b35, #ff1744),
        linear-gradient(145deg, rgba(255, 23, 68, 0.9), rgba(255, 107, 53, 0.9));
    box-shadow:
        0 12px 35px rgba(220, 20, 60, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 30px rgba(255, 107, 53, 0.5);
    transform: translateY(-3px);
}

.spin-button:hover::before {
    left: 100%;
}

.spin-button:active {
    transform: translateY(-1px) scale(0.98);
}

.spin-button:disabled {
    background:
        linear-gradient(145deg, #666, #444),
        linear-gradient(145deg, rgba(102, 102, 102, 0.9), rgba(68, 68, 68, 0.9));
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.6;
}

.max-bet {
    width: 90px;
    height: 50px;
    font-size: 0.9rem;
}

/* Premium Multiplier Display */
.multiplier-display {
    position: absolute;
    top: 50%;
    left: 30px;
    transform: translateY(-50%);
    background:
        linear-gradient(135deg, var(--card-bg), rgba(255, 215, 0, 0.2)),
        var(--glass-bg);
    border: 2px solid var(--primary-gold);
    border-radius: 20px;
    padding: 15px 20px;
    text-align: center;
    backdrop-filter: blur(15px);
    box-shadow:
        var(--shadow-glow),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: multiplierPulse 2s infinite;
    min-width: 100px;
}

.multiplier-text {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.multiplier-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--primary-gold);
    text-shadow:
        var(--text-shadow),
        0 0 20px rgba(255, 215, 0, 0.8);
    animation: valueGlow 1.5s ease-in-out infinite alternate;
}

/* Premium Free Spins Display */
.freespins-display {
    position: absolute;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    background:
        linear-gradient(135deg, var(--card-bg), rgba(138, 43, 226, 0.3)),
        var(--glass-bg);
    border: 2px solid var(--secondary-purple);
    border-radius: 20px;
    padding: 15px 20px;
    text-align: center;
    backdrop-filter: blur(15px);
    box-shadow:
        0 0 30px rgba(138, 43, 226, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: freeSpinsGlow 2s infinite alternate;
    min-width: 120px;
}

.freespins-text {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.freespins-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--secondary-purple);
    text-shadow:
        0 0 15px rgba(138, 43, 226, 0.8),
        0 0 25px rgba(221, 160, 221, 0.6);
    animation: valueGlow 1.5s ease-in-out infinite alternate;
}

/* Premium Win Celebration */
.win-celebration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1000;
    animation: premiumWinCelebration 4s ease-out;
    padding: 30px;
    background:
        radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%),
        var(--glass-bg);
    border-radius: 25px;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 215, 0, 0.5);
}

.win-amount-big {
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--primary-gold), var(--neon-pink), var(--primary-gold));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow:
        0 0 30px rgba(255, 215, 0, 0.8),
        0 0 50px rgba(255, 20, 147, 0.6);
    margin-bottom: 15px;
    animation:
        winAmountPulse 1s ease-in-out infinite alternate,
        gradientShift 3s ease-in-out infinite;
}

.win-text {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-red);
    text-shadow:
        0 0 20px rgba(220, 20, 60, 0.8),
        0 0 30px rgba(255, 107, 53, 0.6);
    text-transform: uppercase;
    letter-spacing: 2px;
    animation: winTextGlow 1.5s ease-in-out infinite alternate;
}

/* Premium Loading Screen */
.loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 30%, rgba(138, 43, 226, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(220, 20, 60, 0.3) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #0f3460 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.loading-content {
    text-align: center;
    padding: 40px;
    background: var(--glass-bg);
    border-radius: 25px;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: var(--shadow-glow);
}

.loading-spinner {
    width: 80px;
    height: 80px;
    border: 6px solid rgba(255, 215, 0, 0.2);
    border-top: 6px solid var(--primary-gold);
    border-right: 6px solid var(--neon-pink);
    border-radius: 50%;
    animation: premiumSpin 1.5s linear infinite;
    margin: 0 auto 25px;
    position: relative;
}

.loading-spinner::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(138, 43, 226, 0.3);
    border-top: 3px solid var(--secondary-purple);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: premiumSpin 1s linear infinite reverse;
}

.loading-text {
    font-size: 1.4rem;
    color: var(--primary-gold);
    font-weight: 700;
    text-shadow: var(--text-shadow);
    animation: loadingTextPulse 2s ease-in-out infinite;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Premium Casino Animations */
@keyframes shimmer {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes premiumSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes multiplierPulse {
    0%, 100% {
        transform: translateY(-50%) scale(1);
        box-shadow: var(--shadow-glow);
    }
    50% {
        transform: translateY(-50%) scale(1.05);
        box-shadow:
            var(--shadow-glow),
            0 0 40px rgba(255, 215, 0, 0.6);
    }
}

@keyframes freeSpinsGlow {
    0% {
        box-shadow: 0 0 30px rgba(138, 43, 226, 0.4);
    }
    100% {
        box-shadow:
            0 0 40px rgba(138, 43, 226, 0.6),
            0 0 60px rgba(221, 160, 221, 0.4);
    }
}

@keyframes valueGlow {
    0% {
        filter: brightness(1) drop-shadow(0 0 10px currentColor);
    }
    100% {
        filter: brightness(1.2) drop-shadow(0 0 20px currentColor);
    }
}

@keyframes premiumWinCelebration {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(-10deg);
        opacity: 0;
    }
    15% {
        transform: translate(-50%, -50%) scale(1.3) rotate(5deg);
        opacity: 1;
    }
    30% {
        transform: translate(-50%, -50%) scale(0.9) rotate(-2deg);
        opacity: 1;
    }
    45% {
        transform: translate(-50%, -50%) scale(1.1) rotate(1deg);
        opacity: 1;
    }
    60% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 1;
    }
    85% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
        opacity: 0;
    }
}

@keyframes winAmountPulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

@keyframes winTextGlow {
    0% {
        text-shadow:
            0 0 20px rgba(220, 20, 60, 0.8),
            0 0 30px rgba(255, 107, 53, 0.6);
    }
    100% {
        text-shadow:
            0 0 30px rgba(220, 20, 60, 1),
            0 0 50px rgba(255, 107, 53, 0.8),
            0 0 70px rgba(255, 20, 147, 0.6);
    }
}

@keyframes loadingTextPulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

@keyframes messageSlideIn {
    0% { transform: translate(-50%, -50%) translateY(-20px); opacity: 0; }
    100% { transform: translate(-50%, -50%) translateY(0); opacity: 1; }
}

@keyframes messageSlideOut {
    0% { transform: translate(-50%, -50%) translateY(0); opacity: 1; }
    100% { transform: translate(-50%, -50%) translateY(20px); opacity: 0; }
}

@keyframes valueChange {
    0% { transform: scale(1); }
    50% { transform: scale(1.15); filter: brightness(1.3); }
    100% { transform: scale(1); }
}

@keyframes jackpotFlash {
    0%, 100% {
        color: var(--primary-gold);
        text-shadow: var(--text-shadow);
    }
    50% {
        color: var(--neon-pink);
        text-shadow:
            0 0 20px rgba(255, 20, 147, 0.8),
            0 0 40px rgba(255, 215, 0, 0.6);
    }
}

/* Premium UI States */
.value-changing {
    animation: valueChange 0.4s ease-out;
}

.spinning .spin-text {
    animation: premiumSpin 1s linear infinite;
}

.insufficient-funds {
    opacity: 0.5;
    cursor: not-allowed !important;
    filter: grayscale(0.7);
}

.high-multiplier {
    animation: multiplierPulse 1s infinite, valueGlow 2s infinite alternate;
}

.mega {
    color: var(--neon-pink) !important;
    text-shadow:
        0 0 30px rgba(255, 20, 147, 0.8) !important,
        0 0 50px rgba(255, 20, 147, 0.6) !important;
    animation: jackpotFlash 1s ease-in-out infinite;
}

.super {
    color: var(--accent-red) !important;
    text-shadow:
        0 0 25px rgba(220, 20, 60, 0.8) !important,
        0 0 40px rgba(255, 107, 53, 0.6) !important;
}

.big {
    color: var(--primary-gold) !important;
    text-shadow:
        0 0 20px rgba(255, 215, 0, 0.8) !important,
        0 0 35px rgba(255, 215, 0, 0.6) !important;
}

/* Mobile-specific adjustments */
.mobile-layout .multiplier-display,
.mobile-layout .freespins-display {
    position: relative;
    transform: none;
    margin: 8px;
    display: inline-block;
}

/* Animation pause state */
.animations-paused * {
    animation-play-state: paused !important;
}

/* Premium Responsive Design */
@media (max-width: 1024px) {
    .game-header {
        padding: 12px 20px;
    }

    .game-title {
        font-size: 1.8rem;
    }

    .multiplier-display,
    .freespins-display {
        left: 20px;
        right: 20px;
        min-width: 80px;
        padding: 10px 15px;
    }
}

@media (max-width: 768px) {
    body::before {
        display: none; /* Remove subtle patterns on mobile for performance */
    }

    .game-header {
        padding: 10px 15px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .game-title {
        font-size: 1.6rem;
        order: 1;
        width: 100%;
        text-align: center;
    }

    .game-stats {
        gap: 15px;
        order: 2;
        width: 100%;
        justify-content: center;
    }

    .stat-item {
        min-width: 70px;
        padding: 6px 10px;
    }

    .stat-label {
        font-size: 0.7rem;
    }

    .stat-value {
        font-size: 1rem;
    }

    .game-controls {
        padding: 15px 20px;
        flex-wrap: wrap;
        gap: 15px;
        justify-content: center;
    }

    .control-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .bet-display {
        margin: 0 10px;
        min-width: 100px;
        padding: 10px 15px;
    }

    .spin-button {
        width: 120px;
        height: 60px;
        font-size: 1.1rem;
    }

    .max-bet {
        width: 80px;
        height: 45px;
        font-size: 0.85rem;
    }

    .multiplier-display,
    .freespins-display {
        position: static;
        transform: none;
        margin: 8px;
        display: inline-block;
        min-width: 90px;
    }

    .win-amount-big {
        font-size: 2.5rem;
    }

    .win-text {
        font-size: 1.4rem;
    }

    .loading-content {
        padding: 30px;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border-width: 4px;
    }

    .loading-text {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .game-header {
        padding: 8px 12px;
    }

    .game-title {
        font-size: 1.4rem;
    }

    .game-stats {
        gap: 12px;
    }

    .stat-item {
        min-width: 60px;
        padding: 5px 8px;
    }

    .game-controls {
        flex-direction: column;
        gap: 12px;
        padding: 12px 15px;
        align-items: center;
    }

    .bet-controls {
        order: 2;
        width: 100%;
        justify-content: center;
    }

    .control-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .bet-display {
        margin: 0 8px;
        min-width: 90px;
        padding: 8px 12px;
    }

    .spin-button {
        order: 1;
        width: 140px;
        height: 65px;
        font-size: 1.2rem;
    }

    .max-bet {
        order: 3;
        width: 100px;
        height: 40px;
        font-size: 0.8rem;
    }

    .win-celebration {
        padding: 20px;
        margin: 0 10px;
    }

    .win-amount-big {
        font-size: 2rem;
    }

    .win-text {
        font-size: 1.2rem;
    }

    .loading-content {
        padding: 25px;
        margin: 0 15px;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border-width: 3px;
    }

    .loading-text {
        font-size: 1rem;
    }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
    .control-btn,
    .spin-button {
        min-height: 44px; /* iOS accessibility guidelines */
    }

    .control-btn:active,
    .spin-button:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    .stat-item:hover {
        transform: none; /* Disable hover effects on touch devices */
    }
}
